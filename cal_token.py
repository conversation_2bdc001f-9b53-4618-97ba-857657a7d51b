import tiktoken

# 模型对应的tokenizer名称，o3-mini一般可用"gpt-4o-mini"或者"o3-mini"（如果没有，可以用类似gpt-4o的tokenizer）
# 这里暂用"gpt-4o-mini"，你可根据官方文档调整
# model_name = "gpt-4o-mini"  
model_name = "o3-mini"

# 价格信息
input_price_per_1k = 0.0011
output_price_per_1k = 0.0044

# 总条数
total_samples = 1532

# 示例输入（你可以用你的实际数据替换下面内容）
system_instruction = """
You are ChatGPT o3-mini. Your goal is to read the provided textual description of an audio or video clip, along with its Valence/Arousal (V/A) values and additional guidelines, and then generate answers based on the questions provided. Each Q&A pair should be formatted in JSON, following the provided examples.
"""

user_instruction = """
1. Task Description:  
   - You will be provided with:
       a textual description of the content of a video or audio clip, serving as an objective content description;
       a pair of V/A values describing the sentiment of the video or audio clip; 
       several related questions.
     Given this textual description and V/A values, you will be asked to answer the corresponding questions, generating question-answer pairs suitable for the instruction-following dataset.

2. Video or Audio Information (Textual Description):  
   The video opens with a close-up of a golden ring on a black, textured surface, possibly armor, with a fiery glow suggesting power or magic. The scene shifts to a chaotic battle with numerous armored figures in a dark, foggy environment, indicating a war or conflict. A character in armor is seen in a close-up, looking determined or focused. The camera then shows a character lying on the ground, seemingly injured or dead, with a gloved hand reaching towards them, suggesting a moment of despair or finality. The environment is dark and gritty, with rocky textures and dim lighting, enhancing the grim atmosphere. The final frame shows a character in dark armor with a menacing appearance, raising their hand, which has the same golden ring, against a backdrop of dark clouds, indicating a climactic or ominous moment.

3. V/A Value (Valence/Arousal):  
   - The V/A range is from -1 to 1, where:  
     • Valence (V) indicates how positive (+) or negative (−) an emotional state is.  
     • Arousal (A) indicates the intensity of the emotion, from calm (−) to excited (+).  
   - Example Range and Meaning:  
     • V = -1.0 → Very negative; V = 1.0 → Very positive  
     • A = -1.0 → Extremely calm/low energy; A = 1.0 → Highly excited/energetic  
   - Provided V/A values for this clip: <-0.81, 0.65> 

4. V/A Guidelines or Examples:  
   - You can use the V/A values to describe general mood (e.g., cheerful, anxious, somber).  
   - For instance, if V > 0 and A > 0, you might describe the overall tone as “excited” or “enthusiastic.”
   - If V < 0 and A < 0, you might describe the overall tone as “sad” or “depressed.”
   - Determine the emotion of this clip based on the specific value.

5. Questions to Ask in the Q&A Pairs:  
   - The questions will be divided into two categories, content or emotion, and I will tell you the type and content of the questions in a clear format.
   - For each question, provide a concise response reflecting the textual description and the emotional context.
   - Question: 
       1. (content) What is the content of this video?
       2. (emotion) What is the emotion of this video?

6. Example Q&A Format (in JSON):  
  [
    {
      "question": "What is the content of this video?",
      "response": "The video shows two men conversing outdoors against a weathered stone wall. One man with graying hair, wearing a light blue shirt, listens attentively while the other man, dressed in a light blue denim shirt, speaks using hand gestures and facial expressions. The scene focuses on their interaction and body language, suggesting a serious discussion.",
      "type": "content"
    },
    {
      "question": "What is the emotion of the video?",
      "response": "The scene conveys a calm yet serious mood. The valence (0.14) indicates a slightly positive but nearly neutral emotional tone, while the arousal (-0.7) suggests the energy level is quite low or subdued, contributing to the overall somber and focused atmosphere.",
      "type": "emotion"
    }
  ]
"""

# 示例模型输出 (回答)
model_output = """
[
  {
    "question": "What is the content of this video?",
    "response": "The video opens with a close-up of a golden ring resting on a textured, possibly armored surface, illuminated by a fiery glow that hints at power or magic. It then shifts to a chaotic battle scene where numerous armored figures engage in combat within a dark, foggy environment, suggesting an ongoing conflict or war. The camera focuses on a determined character in armor, then moves to a scene showing another character lying on the ground, injured or dead, with a gloved hand reaching toward them, implying despair or a critical moment. The video concludes with a climactic shot of a menacing character in dark armor, raising a hand adorned with the same golden ring against a backdrop of dark clouds, underlining the ominous tone of the narrative.",
    "type": "content"
  },
  {
    "question": "What is the emotion of the video?",
    "response": "The video exudes a predominantly negative and intense mood. With a valence of -0.81, the overall tone is somber and foreboding, reflecting themes of despair and finality. The arousal value of 0.65 adds a layer of high energy and tension, indicative of a dynamic and chaotic battlefield. Together, these elements evoke feelings of unease, urgency, and menace throughout the clip.",
    "type": "emotion"
  }
]
"""

def count_tokens(text: str, tokenizer) -> int:
    tokens = tokenizer.encode(text)
    return len(tokens)

def main():
    tokenizer = tiktoken.encoding_for_model(model_name)

    # 合并输入所有文本，计算输入token数
    full_input_text = system_instruction + "\n" + user_instruction
    input_tokens = count_tokens(full_input_text, tokenizer)

    # 输出是模型回答，计算输出token数
    output_tokens = count_tokens(model_output, tokenizer)

    # 计算单条费用
    input_cost = (input_tokens / 1000) * input_price_per_1k
    output_cost = (output_tokens / 1000) * output_price_per_1k
    single_sample_cost = input_cost + output_cost

    # 计算总费用
    # 假设每个样本要问10个问题
    total_cost = single_sample_cost * total_samples * 2 * 10
    print(f"模型名称: {model_name}")
    print(f"单条单个问题样本输入Token数: {input_tokens}")
    print(f"单条单个问题样本输出Token数: {output_tokens}")
    print(f"单条单个问题样本费用: ${single_sample_cost:.6f}")
    print(f"{total_samples}条样本全部问题总费用(音频+视频): ${total_cost:.2f}")

if __name__ == "__main__":
    main()
